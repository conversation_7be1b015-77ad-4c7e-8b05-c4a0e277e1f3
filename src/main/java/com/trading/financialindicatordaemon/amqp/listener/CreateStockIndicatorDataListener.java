package com.trading.financialindicatordaemon.amqp.listener;

import com.rabbitmq.client.Channel;
import com.trading.financialindicatordaemon.config.RabbitMqConfig;
import com.trading.financialindicatordaemon.mapper.StockCandleDataSymbolAndConversionCurrency;
import com.trading.financialindicatordaemon.service.indicator.IndicatorDataService;
import com.trading.financialindicatordaemon.service.mining.DataMiningService;
import com.trading.financialindicatordaemon.service.stock.StockCandleDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class CreateStockIndicatorDataListener extends BaseRabbitMqListener {

    private static final Logger logger = LoggerFactory.getLogger(CreateStockIndicatorDataListener.class);
    private final IndicatorDataService indicatorDataService;
    private final StockCandleDataService stockCandleDataService;

    public CreateStockIndicatorDataListener(DataMiningService dataMiningService,
                                            IndicatorDataService indicatorDataService,
                                            StockCandleDataService stockCandleDataService) {
        super(dataMiningService);
        this.indicatorDataService = indicatorDataService;
        this.stockCandleDataService = stockCandleDataService;
    }

    @RabbitListener(queues = RabbitMqConfig.CREATE_STOCK_INDICATOR_DATA)
    public void handleCreateStockIndicatorData(@Payload Map<String, Object> ignoredMessage,
                                               Channel channel,
                                               @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                               Message amqpMessage) {
        logger.info("Processing create_stock_indicator_data message");
        handleSimpleMessage("create_stock_indicator_data", channel, deliveryTag, amqpMessage,
                () -> {
                    stockCandleDataService.findAllSymbolAndConversionCurrency()
                            .forEach(
                                    symbolAndConversionCurrency ->
                                            indicatorDataService.calculateForStock(
                                                    symbolAndConversionCurrency.getSymbol(),
                                                    symbolAndConversionCurrency.getConversionCurrency())
                            );

                    logger.info("Calculating BTC indicators for all stocks");
                    stockCandleDataService.findAllSymbolAndConversionCurrency()
                            .stream()
                            .filter(sc -> "USD".equals(sc.getConversionCurrency()))
                            .map(StockCandleDataSymbolAndConversionCurrency::getSymbol)
                            .distinct()
                            .forEach(indicatorDataService::calculateForStockInBtc);
                });
    }

}
