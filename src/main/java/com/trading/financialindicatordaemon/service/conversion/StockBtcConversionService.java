package com.trading.financialindicatordaemon.service.conversion;

import com.trading.financialindicatordaemon.client.cmc.CryptoCandleHistoricalQuote;
import com.trading.financialindicatordaemon.service.cmc.CmcCandleDataService;
import com.trading.financialindicatordaemon.service.stock.StockCandleData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.math.RoundingMode.HALF_UP;
import static java.time.ZoneOffset.UTC;
import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;

@Service
public class StockBtcConversionService {
    private static final Logger logger = LoggerFactory.getLogger(StockBtcConversionService.class);
    private static final int BTC_PRECISION = 8;

    private final CmcCandleDataService cmcCandleDataService;

    public StockBtcConversionService(CmcCandleDataService cmcCandleDataService) {
        this.cmcCandleDataService = cmcCandleDataService;
    }

    public List<StockCandleData> convertStockDataToBtc(List<StockCandleData> stockUsdData) {
        if (stockUsdData.isEmpty()) {
            logger.warn("No stock USD data provided for BTC conversion");
            return List.of();
        }

        String symbol = stockUsdData.getFirst().getSymbol();
        logger.info("Converting {} USD stock candles to BTC for symbol: {}", stockUsdData.size(), symbol);

        List<CryptoCandleHistoricalQuote> btcUsdData = cmcCandleDataService.find("BTC", "USD");
        if (btcUsdData.isEmpty()) {
            logger.error("No Bitcoin USD data available for conversion");
            return List.of();
        }

        Map<LocalDate, CryptoCandleHistoricalQuote> btcPricesByDate = createBtcPriceMap(btcUsdData);

        List<StockCandleData> btcStockData = stockUsdData.stream()
                .map(stockCandle -> convertStockCandleToBtc(stockCandle, btcPricesByDate))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());

        logger.info("Successfully converted {} stock candles to BTC for symbol: {}", btcStockData.size(), symbol);
        return btcStockData;
    }

    private Map<LocalDate, CryptoCandleHistoricalQuote> createBtcPriceMap(List<CryptoCandleHistoricalQuote> btcData) {
        return btcData.stream()
                .collect(Collectors.toMap(
                        quote -> parseTimestampToDate(quote.getQuote().getTimestamp()),
                        quote -> quote,
                        (existing, replacement) -> existing // Keep first if duplicates
                ));
    }

    private Optional<StockCandleData> convertStockCandleToBtc(StockCandleData stockUsdCandle,
                                                              Map<LocalDate, CryptoCandleHistoricalQuote> btcPricesByDate) {
        LocalDate stockDate = stockUsdCandle.getTimestamp();

        Optional<CryptoCandleHistoricalQuote> possibleBtcQuote = findBtcPriceForDate(stockDate, btcPricesByDate);
        if (possibleBtcQuote.isEmpty()) {
            logger.debug("No Bitcoin price found for date: {} (stock: {})", stockDate, stockUsdCandle.getSymbol());
            return Optional.empty();
        }

        CryptoCandleHistoricalQuote btcQuote = possibleBtcQuote.get();
        StockCandleData btcStockCandle = new StockCandleData();
        btcStockCandle.setSymbol(stockUsdCandle.getSymbol());
        btcStockCandle.setTimestamp(stockUsdCandle.getTimestamp());
        btcStockCandle.setConversionCurrency("BTC");
        btcStockCandle.setVolume(stockUsdCandle.getVolume());

        CryptoCandleHistoricalQuote.Quote quote = btcQuote.getQuote();
        BigDecimal hl2 = quote.getHigh().add(quote.getLow()).divide(BigDecimal.valueOf(2), BTC_PRECISION, HALF_UP);

        btcStockCandle.setOpen(divideByBtcPrice(stockUsdCandle.getOpen(), hl2));
        btcStockCandle.setHigh(divideByBtcPrice(stockUsdCandle.getHigh(), hl2));
        btcStockCandle.setLow(divideByBtcPrice(stockUsdCandle.getLow(), hl2));
        btcStockCandle.setClose(divideByBtcPrice(stockUsdCandle.getClose(), hl2));

        return Optional.of(btcStockCandle);
    }

    private Optional<CryptoCandleHistoricalQuote> findBtcPriceForDate(
            LocalDate targetDate,
            Map<LocalDate, CryptoCandleHistoricalQuote> btcPricesByDate) {
        return Optional.ofNullable(btcPricesByDate.get(targetDate));
    }

    private BigDecimal divideByBtcPrice(BigDecimal stockPrice, BigDecimal btcPrice) {
        return stockPrice.divide(btcPrice, BTC_PRECISION, HALF_UP);
    }

    private LocalDate parseTimestampToDate(String timestamp) {
        if (timestamp.contains("T")) {
            return java.time.Instant.parse(timestamp)
                    .atZone(UTC)
                    .toLocalDate();
        } else {
            return LocalDate.parse(timestamp, ISO_LOCAL_DATE);
        }
    }

}
