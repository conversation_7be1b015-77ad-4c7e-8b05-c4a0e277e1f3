package com.trading.financialindicatordaemon.service.indicator;

import com.trading.financialindicatordaemon.client.cmc.CryptoCandleHistoricalQuote;
import com.trading.financialindicatordaemon.client.indicatorapi.CalculateIndicatorsRequest;
import com.trading.financialindicatordaemon.client.indicatorapi.IndicatorApiClient;
import com.trading.financialindicatordaemon.client.indicatorapi.IndicatorData;
import com.trading.financialindicatordaemon.mapper.IndicatorDataMapper;
import com.trading.financialindicatordaemon.mapper.IndicatorDataWrapper;
import com.trading.financialindicatordaemon.service.cmc.CmcCandleDataService;
import com.trading.financialindicatordaemon.service.conversion.StockBtcConversionService;
import com.trading.financialindicatordaemon.service.stock.StockCandleData;
import com.trading.financialindicatordaemon.service.stock.StockCandleDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static java.math.BigDecimal.ZERO;

@Service
public class IndicatorDataService {

    private static final Logger logger = LoggerFactory.getLogger(IndicatorDataService.class);

    private final IndicatorApiClient indicatorApiClient;
    private final CmcCandleDataService cmcCandleDataService;
    private final IndicatorDataMapper indicatorDataMapper;
    private final StockCandleDataService stockCandleDataService;
    private final StockBtcConversionService stockBtcConversionService;

    public IndicatorDataService(@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
                                IndicatorApiClient indicatorApiClient,
                                CmcCandleDataService cmcCandleDataService,
                                IndicatorDataMapper indicatorDataMapper,
                                StockCandleDataService stockCandleDataService,
                                StockBtcConversionService stockBtcConversionService) {
        this.indicatorApiClient = indicatorApiClient;
        this.cmcCandleDataService = cmcCandleDataService;
        this.indicatorDataMapper = indicatorDataMapper;
        this.stockCandleDataService = stockCandleDataService;
        this.stockBtcConversionService = stockBtcConversionService;
    }

    public void calculateForCrypto(String symbol, String conversionCurrency) {
        List<CryptoCandleHistoricalQuote> quotes = cmcCandleDataService.find(symbol, conversionCurrency);
        List<CalculateIndicatorsRequest> requests = quotes.stream()
                .sorted(Comparator.comparing(q -> q.getQuote().getTimestamp()))
                .map(
                        quote -> {
                            CalculateIndicatorsRequest request = new CalculateIndicatorsRequest();
                            request.setClose(quote.getQuote().getClose());
                            request.setHigh(quote.getQuote().getHigh());
                            request.setLow(quote.getQuote().getLow());
                            request.setMarketCap(quote.getQuote().getMarketCap());
                            request.setName(quote.getQuote().getName());
                            request.setOpen(quote.getQuote().getOpen());
                            request.setTimestamp(quote.getQuote().getTimestamp());
                            request.setVolume(quote.getQuote().getVolume());
                            return request;
                        }
                ).toList();

        Optional<IndicatorDataWrapper> latestByCoinAndCurrency =
                indicatorDataMapper.findLatestByCoinAndCurrency(symbol, conversionCurrency, "CRYPTO");

        if (latestByCoinAndCurrency.isPresent() && latestDataExists(latestByCoinAndCurrency.get(), requests)) {
            logger.info("Latest indicator data already exists for {}/{}", symbol, conversionCurrency);
            return;
        }

        calculate(symbol, conversionCurrency, requests);
    }

    public void calculateForStock(String symbol, String conversionCurrency) {
        List<StockCandleData> quotes = stockCandleDataService.find(symbol, conversionCurrency);
        List<CalculateIndicatorsRequest> requests = quotes.stream()
                .sorted(Comparator.comparing(StockCandleData::getTimestamp))
                .map(
                        quote -> {
                            CalculateIndicatorsRequest request = new CalculateIndicatorsRequest();
                            request.setClose(quote.getClose());
                            request.setHigh(quote.getHigh());
                            request.setLow(quote.getLow());
                            request.setOpen(quote.getOpen());
                            request.setTimestamp(quote.getTimestamp().toString());
                            request.setVolume(BigDecimal.valueOf(quote.getVolume()));
                            request.setName(quote.getSymbol());
                            request.setMarketCap(ZERO);
                            return request;
                        }
                ).toList();

        Optional<IndicatorDataWrapper> latestByCoinAndCurrency =
                indicatorDataMapper.findLatestByCoinAndCurrency(symbol, conversionCurrency, "STOCK");

        if (latestByCoinAndCurrency.isPresent() && latestDataExists(latestByCoinAndCurrency.get(), requests)) {
            logger.info("Latest indicator data already exists for {}/{}", symbol, conversionCurrency);
            return;
        }

        try {
            calculate(symbol, conversionCurrency, requests);
        } catch (Exception e) {
            logger.error("Failed to calculate indicators for {}/{}", symbol, conversionCurrency, e);
        }
    }

    public void calculateForStockInBtc(String symbol) {
        logger.info("Calculating BTC indicators for stock symbol: {}", symbol);

        List<StockCandleData> usdStockData = stockCandleDataService.find(symbol, "USD");
        if (usdStockData.isEmpty()) {
            logger.warn("No USD stock data found for symbol: {}", symbol);
            return;
        }

        List<StockCandleData> btcStockData = stockBtcConversionService.convertStockDataToBtc(usdStockData);
        if (btcStockData.isEmpty()) {
            logger.warn("Failed to convert stock data to BTC for symbol: {}", symbol);
            return;
        }

        Optional<IndicatorDataWrapper> existingBtcData =
                indicatorDataMapper.findLatestByCoinAndCurrency(symbol, "BTC", "STOCK");

        List<CalculateIndicatorsRequest> requests = btcStockData.stream()
                .sorted(Comparator.comparing(StockCandleData::getTimestamp))
                .map(quote -> {
                    CalculateIndicatorsRequest request = new CalculateIndicatorsRequest();
                    request.setClose(quote.getClose());
                    request.setHigh(quote.getHigh());
                    request.setLow(quote.getLow());
                    request.setOpen(quote.getOpen());
                    request.setTimestamp(quote.getTimestamp().toString());
                    request.setVolume(BigDecimal.valueOf(quote.getVolume()));
                    request.setName(quote.getSymbol());
                    request.setMarketCap(ZERO);
                    return request;
                }).toList();

        if (existingBtcData.isPresent() && latestDataExists(existingBtcData.get(), requests)) {
            logger.info("Latest BTC indicator data already exists for stock: {}", symbol);
            return;
        }

        try {
            calculate(symbol, "BTC", requests);
            logger.info("Successfully calculated BTC indicators for stock: {}", symbol);
        } catch (Exception e) {
            logger.error("Failed to calculate BTC indicators for stock: {}", symbol, e);
        }
    }

    private void calculate(String symbol, String conversionCurrency, List<CalculateIndicatorsRequest> requests) {
        logger.info("Calculating indicators for {}/{}", symbol, conversionCurrency);
        ResponseEntity<List<IndicatorData>> response = indicatorApiClient.calculateIndicators(requests);

        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("Failed to calculate indicators: " + response.getStatusCode());
        }

        List<IndicatorData> responseBody = response.getBody();
        logger.info("Indicator calculation completed for {}/{} - received {} records",
                symbol, conversionCurrency, responseBody != null ? responseBody.size() : 0);

        logger.info("Inserting indicator data for {}/{}", symbol, conversionCurrency);
        insert(symbol, conversionCurrency, responseBody);
    }

    public List<IndicatorData> findForCrypto(String symbol, String conversionCurrency) {
        Optional<IndicatorDataWrapper> wrapper =
                indicatorDataMapper.findLatestByCoinAndCurrency(symbol, conversionCurrency, "CRYPTO");

        if (wrapper.isPresent()) {
            return wrapper.get().getIndicatorValues();
        }

        logger.warn("No indicator data found for symbol: {} and currency: {}", symbol, conversionCurrency);
        return List.of();
    }

    public List<IndicatorData> findForStock(String symbol, String conversionCurrency) {
        Optional<IndicatorDataWrapper> wrapper =
                indicatorDataMapper.findLatestByCoinAndCurrency(symbol, conversionCurrency, "STOCK");

        if (wrapper.isPresent()) {
            return wrapper.get().getIndicatorValues();
        }

        logger.warn("No indicator data found for symbol: {} and currency: {}", symbol, conversionCurrency);
        return List.of();
    }

    public void insert(String symbol, String conversionCurrency, List<IndicatorData> data) {
        indicatorDataMapper.insert(symbol, conversionCurrency, data);
    }

    public List<IndicatorDataWrapper> findAllCryptoWithLatestIndicatorDataOnly() {
        return indicatorDataMapper.findAllWithLatestIndicatorDataOnly("CRYPTO");
    }

    public List<IndicatorDataWrapper> findAllStockWithLatestIndicatorDataOnly() {
        return indicatorDataMapper.findAllWithLatestIndicatorDataOnly("STOCK");
    }

    private boolean latestDataExists(IndicatorDataWrapper latestByCoinAndCurrency,
                                     List<CalculateIndicatorsRequest> requests) {
        return latestByCoinAndCurrency
                .getIndicatorValues()
                .getFirst()
                .getTimestamp().equals(
                        requests
                                .getLast()
                                .getTimestamp());
    }

}
